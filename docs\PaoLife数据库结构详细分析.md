# PaoLife项目数据库结构详细分析

## 第一部分：数据库结构详细分析

### 数据库概览

PaoLife项目使用SQLite作为本地数据库，通过Kysely ORM进行类型安全的数据访问。数据库包含18个核心业务表，完整实现了P.A.R.A.方法论的数据模型。

### 数据库表分类

#### 1. 用户和配置类 (2个表)
- `User` - 用户信息表
- 配置通过ConfigManager管理，存储在JSON文件中

#### 2. 项目管理类 (5个表)
- `Project` - 项目基本信息
- `ProjectKPI` - 项目关键绩效指标
- `KPIRecord` - KPI历史记录
- `Deliverable` - 项目交付物
- `DeliverableResource` - 交付物资源关联

#### 3. 领域管理类 (6个表)
- `Area` - 领域基本信息
- `AreaMetric` - 领域指标
- `AreaMetricRecord` - 领域指标记录
- `Habit` - 习惯定义
- `HabitRecord` - 习惯打卡记录
- `RecurringTask` - 定期维护任务

#### 4. 任务管理类 (3个表)
- `Task` - 任务信息
- `Tag` - 标签定义
- `TaskTag` - 任务标签关联

#### 5. 资源管理类 (2个表)
- `ResourceLink` - 资源文件关联
- `DocumentLink` - 文档双向链接

#### 6. 其他功能类 (4个表)
- `Checklist` - 清单模板
- `ChecklistInstance` - 清单实例
- `Review` - 回顾记录
- `ReviewTemplate` - 回顾模板
- `InboxNote` - 收件箱笔记

## 详细表结构定义

### 1. 用户和配置类

#### User 表
```sql
CREATE TABLE IF NOT EXISTS "User" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "username" TEXT NOT NULL UNIQUE,
  "password" TEXT NOT NULL,
  "settings" TEXT  -- JSON格式存储用户设置
);
```

**字段说明**:
- `id`: 主键，用户唯一标识符
- `username`: 用户名，唯一约束
- `password`: 密码（加密存储）
- `settings`: JSON格式的用户设置，包含主题、语言等配置

**索引**: 
- PRIMARY KEY (`id`)
- UNIQUE INDEX (`username`)

### 2. 项目管理类

#### Project 表
```sql
CREATE TABLE IF NOT EXISTS "Project" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "status" TEXT NOT NULL DEFAULT 'Not Started',
  "progress" INTEGER NOT NULL DEFAULT 0,
  "goal" TEXT,
  "deliverable" TEXT,
  "startDate" TEXT,
  "deadline" TEXT,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "archived" INTEGER NOT NULL DEFAULT 0,
  "areaId" TEXT,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，项目唯一标识符
- `name`: 项目名称，必填
- `description`: 项目描述
- `status`: 项目状态 ('Not Started', 'In Progress', 'At Risk', 'Paused', 'Completed')
- `progress`: 项目进度 (0-100)
- `goal`: 项目目标描述
- `deliverable`: 最终交付物描述
- `startDate`: 开始日期 (ISO字符串)
- `deadline`: 截止日期 (ISO字符串)
- `createdAt`: 创建时间
- `updatedAt`: 更新时间
- `archived`: 归档状态 (0=未归档, 1=已归档)
- `areaId`: 关联领域ID，外键

**外键关系**:
- `areaId` → `Area.id` (SET NULL ON DELETE)

#### ProjectKPI 表
```sql
CREATE TABLE IF NOT EXISTS "ProjectKPI" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "value" TEXT NOT NULL,
  "target" TEXT,
  "unit" TEXT,
  "frequency" TEXT,
  "direction" TEXT NOT NULL DEFAULT 'increase',
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "projectId" TEXT NOT NULL,
  FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，KPI唯一标识符
- `name`: KPI名称
- `value`: 当前值
- `target`: 目标值
- `unit`: 单位
- `frequency`: 更新频率 ('daily', 'weekly', 'monthly')
- `direction`: 方向 ('increase'=增长型, 'decrease'=减少型)
- `updatedAt`: 更新时间
- `projectId`: 关联项目ID，外键

**外键关系**:
- `projectId` → `Project.id` (CASCADE ON DELETE)

#### KPIRecord 表
```sql
CREATE TABLE IF NOT EXISTS "KPIRecord" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "value" TEXT NOT NULL,
  "note" TEXT,
  "recordedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "kpiId" TEXT NOT NULL,
  FOREIGN KEY ("kpiId") REFERENCES "ProjectKPI" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，记录唯一标识符
- `value`: 记录值
- `note`: 备注信息
- `recordedAt`: 记录时间
- `kpiId`: 关联KPI ID，外键

**外键关系**:
- `kpiId` → `ProjectKPI.id` (CASCADE ON DELETE)

**索引**:
- `idx_KPIRecord_kpiId_recordedAt` ON (`kpiId`, `recordedAt`)

#### Deliverable 表
```sql
CREATE TABLE IF NOT EXISTS "Deliverable" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "title" TEXT NOT NULL,
  "description" TEXT,
  "type" TEXT NOT NULL DEFAULT 'document',
  "status" TEXT NOT NULL DEFAULT 'planned',
  "content" TEXT,
  "url" TEXT,
  "filePath" TEXT,
  "acceptanceCriteria" TEXT,
  "plannedDate" TEXT,
  "actualDate" TEXT,
  "projectId" TEXT NOT NULL,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，交付物唯一标识符
- `title`: 交付物标题
- `description`: 描述
- `type`: 类型 ('document', 'software', 'presentation', 'report')
- `status`: 状态 ('planned', 'in_progress', 'completed', 'cancelled')
- `content`: 内容
- `url`: 相关URL
- `filePath`: 文件路径
- `acceptanceCriteria`: 验收标准 (JSON格式)
- `plannedDate`: 计划完成日期
- `actualDate`: 实际完成日期
- `projectId`: 关联项目ID，外键

**外键关系**:
- `projectId` → `Project.id` (CASCADE ON DELETE)

#### DeliverableResource 表
```sql
CREATE TABLE IF NOT EXISTS "DeliverableResource" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "deliverableId" TEXT NOT NULL,
  "resourcePath" TEXT NOT NULL,
  "resourceType" TEXT NOT NULL,
  "title" TEXT,
  "description" TEXT,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY ("deliverableId") REFERENCES "Deliverable" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，资源关联唯一标识符
- `deliverableId`: 关联交付物ID，外键
- `resourcePath`: 资源文件路径
- `resourceType`: 资源类型 ('file', 'link', 'reference')
- `title`: 资源标题
- `description`: 资源描述
- `createdAt`: 创建时间

**外键关系**:
- `deliverableId` → `Deliverable.id` (CASCADE ON DELETE)

### 3. 领域管理类

#### Area 表
```sql
CREATE TABLE IF NOT EXISTS "Area" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "standard" TEXT,
  "description" TEXT,
  "icon" TEXT,
  "color" TEXT,
  "archived" INTEGER NOT NULL DEFAULT 0,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明**:
- `id`: 主键，领域唯一标识符
- `name`: 领域名称
- `standard`: 领域标准描述
- `description`: 领域描述
- `icon`: 图标标识
- `color`: 颜色代码
- `archived`: 归档状态 (0=未归档, 1=已归档)
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

#### AreaMetric 表
```sql
CREATE TABLE IF NOT EXISTS "AreaMetric" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "value" TEXT NOT NULL DEFAULT '',
  "target" TEXT,
  "unit" TEXT,
  "trackingType" TEXT NOT NULL DEFAULT 'manual',
  "direction" TEXT NOT NULL DEFAULT 'higher_better',
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "areaId" TEXT NOT NULL,
  "relatedHabits" TEXT,  -- JSON格式存储关联习惯
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，指标唯一标识符
- `name`: 指标名称
- `value`: 当前值
- `target`: 目标值
- `unit`: 单位
- `trackingType`: 追踪类型 ('manual', 'habit', 'automatic')
- `direction`: 方向 ('higher_better', 'lower_better')
- `updatedAt`: 更新时间
- `areaId`: 关联领域ID，外键
- `relatedHabits`: 关联习惯列表 (JSON格式)

**外键关系**:
- `areaId` → `Area.id` (CASCADE ON DELETE)

#### AreaMetricRecord 表
```sql
CREATE TABLE IF NOT EXISTS "AreaMetricRecord" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "value" TEXT NOT NULL,
  "note" TEXT,
  "recordedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "source" TEXT,
  "confidence" REAL,
  "tags" TEXT,  -- JSON格式存储标签
  "context" TEXT,
  "metricId" TEXT NOT NULL,
  FOREIGN KEY ("metricId") REFERENCES "AreaMetric" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，记录唯一标识符
- `value`: 记录值
- `note`: 备注信息
- `recordedAt`: 记录时间
- `source`: 数据来源
- `confidence`: 置信度 (0.0-1.0)
- `tags`: 标签列表 (JSON格式)
- `context`: 上下文信息
- `metricId`: 关联指标ID，外键

**外键关系**:
- `metricId` → `AreaMetric.id` (CASCADE ON DELETE)

**索引**:
- `idx_AreaMetricRecord_metricId_recordedAt` ON (`metricId`, `recordedAt`)

#### Habit 表
```sql
CREATE TABLE IF NOT EXISTS "Habit" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "frequency" TEXT NOT NULL,
  "target" INTEGER NOT NULL,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "areaId" TEXT NOT NULL,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，习惯唯一标识符
- `name`: 习惯名称
- `frequency`: 频率 ('daily', 'weekly', 'monthly')
- `target`: 目标次数
- `createdAt`: 创建时间
- `updatedAt`: 更新时间
- `areaId`: 关联领域ID，外键

**外键关系**:
- `areaId` → `Area.id` (CASCADE ON DELETE)

#### HabitRecord 表
```sql
CREATE TABLE IF NOT EXISTS "HabitRecord" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "date" TEXT NOT NULL,
  "completed" INTEGER NOT NULL DEFAULT 1,
  "value" REAL,
  "note" TEXT,
  "habitId" TEXT NOT NULL,
  FOREIGN KEY ("habitId") REFERENCES "Habit" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
  UNIQUE ("habitId", "date")
);
```

**字段说明**:
- `id`: 主键，记录唯一标识符
- `date`: 日期 (YYYY-MM-DD格式)
- `completed`: 完成状态 (0=未完成, 1=已完成)
- `value`: 数值记录 (可选)
- `note`: 备注信息
- `habitId`: 关联习惯ID，外键

**外键关系**:
- `habitId` → `Habit.id` (CASCADE ON DELETE)

**唯一约束**:
- UNIQUE (`habitId`, `date`) - 每个习惯每天只能有一条记录

#### RecurringTask 表
```sql
CREATE TABLE IF NOT EXISTS "RecurringTask" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "title" TEXT NOT NULL,
  "description" TEXT,
  "repeatRule" TEXT NOT NULL,
  "repeatInterval" INTEGER NOT NULL DEFAULT 1,
  "nextDueDate" TEXT,
  "lastCompletedAt" TEXT,
  "isActive" INTEGER NOT NULL DEFAULT 1,
  "areaId" TEXT NOT NULL,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，定期任务唯一标识符
- `title`: 任务标题
- `description`: 任务描述
- `repeatRule`: 重复规则 ('daily', 'weekly', 'monthly', 'yearly')
- `repeatInterval`: 重复间隔
- `nextDueDate`: 下次到期日期
- `lastCompletedAt`: 最后完成时间
- `isActive`: 是否激活 (0=停用, 1=激活)
- `areaId`: 关联领域ID，外键
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

**外键关系**:
- `areaId` → `Area.id` (CASCADE ON DELETE)

### 4. 任务管理类

#### Task 表
```sql
CREATE TABLE IF NOT EXISTS "Task" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "content" TEXT NOT NULL,
  "description" TEXT,
  "completed" INTEGER NOT NULL DEFAULT 0,
  "priority" TEXT,
  "dueDate" TEXT,
  "completedAt" TEXT,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "parentId" TEXT,
  "projectId" TEXT,
  "areaId" TEXT,
  "position" REAL NOT NULL DEFAULT 0,
  "sourceType" TEXT,
  "sourceId" TEXT,
  "sourceContext" TEXT,
  "resourceLinkId" TEXT,
  FOREIGN KEY ("resourceLinkId") REFERENCES "ResourceLink" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
  FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
  FOREIGN KEY ("parentId") REFERENCES "Task" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，任务唯一标识符
- `content`: 任务内容
- `description`: 任务描述
- `completed`: 完成状态 (0=未完成, 1=已完成)
- `priority`: 优先级 ('low', 'medium', 'high')
- `dueDate`: 截止日期
- `completedAt`: 完成时间
- `createdAt`: 创建时间
- `updatedAt`: 更新时间
- `parentId`: 父任务ID (支持无限层级)
- `projectId`: 关联项目ID
- `areaId`: 关联领域ID
- `position`: 排序位置
- `sourceType`: 来源类型
- `sourceId`: 来源ID
- `sourceContext`: 来源上下文
- `resourceLinkId`: 关联资源ID

**外键关系**:
- `parentId` → `Task.id` (SET NULL ON DELETE) - 支持任务层级
- `projectId` → `Project.id` (SET NULL ON DELETE)
- `areaId` → `Area.id` (SET NULL ON DELETE)
- `resourceLinkId` → `ResourceLink.id` (SET NULL ON DELETE)

#### Tag 表
```sql
CREATE TABLE IF NOT EXISTS "Tag" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL UNIQUE,
  "color" TEXT,
  "icon" TEXT
);
```

**字段说明**:
- `id`: 主键，标签唯一标识符
- `name`: 标签名称，唯一约束
- `color`: 标签颜色
- `icon`: 标签图标

**唯一约束**:
- UNIQUE (`name`) - 标签名称唯一

#### TaskTag 表 (多对多关联表)
```sql
CREATE TABLE IF NOT EXISTS "TaskTag" (
  "taskId" TEXT NOT NULL,
  "tagId" TEXT NOT NULL,
  PRIMARY KEY ("taskId", "tagId"),
  FOREIGN KEY ("tagId") REFERENCES "Tag" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY ("taskId") REFERENCES "Task" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
```

**字段说明**:
- `taskId`: 任务ID，外键
- `tagId`: 标签ID，外键

**主键**:
- PRIMARY KEY (`taskId`, `tagId`) - 复合主键

**外键关系**:
- `taskId` → `Task.id` (CASCADE ON DELETE)
- `tagId` → `Tag.id` (CASCADE ON DELETE)

### 5. 资源管理类

#### ResourceLink 表
```sql
CREATE TABLE IF NOT EXISTS "ResourceLink" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "resourcePath" TEXT NOT NULL,
  "title" TEXT,
  "projectId" TEXT,
  "areaId" TEXT,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，资源链接唯一标识符
- `resourcePath`: 资源文件路径
- `title`: 资源标题
- `projectId`: 关联项目ID (可选)
- `areaId`: 关联领域ID (可选)

**外键关系**:
- `projectId` → `Project.id` (CASCADE ON DELETE)
- `areaId` → `Area.id` (CASCADE ON DELETE)

**业务规则**:
- `projectId` 和 `areaId` 至少有一个不为空
- 同一资源可以同时关联项目和领域

#### DocumentLink 表
```sql
CREATE TABLE IF NOT EXISTS "DocumentLink" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "sourceDocPath" TEXT NOT NULL,
  "sourceDocTitle" TEXT,
  "targetDocPath" TEXT NOT NULL,
  "targetDocTitle" TEXT,
  "linkText" TEXT,
  "contextBefore" TEXT,
  "contextAfter" TEXT,
  "lineNumber" INTEGER,
  "linkType" TEXT NOT NULL DEFAULT 'wikilink',
  "isValid" INTEGER NOT NULL DEFAULT 1,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明**:
- `id`: 主键，文档链接唯一标识符
- `sourceDocPath`: 源文档路径
- `sourceDocTitle`: 源文档标题
- `targetDocPath`: 目标文档路径
- `targetDocTitle`: 目标文档标题
- `linkText`: 链接文本
- `contextBefore`: 链接前上下文
- `contextAfter`: 链接后上下文
- `lineNumber`: 行号
- `linkType`: 链接类型 ('wikilink', 'reference', 'mention')
- `isValid`: 链接有效性 (0=无效, 1=有效)
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

**索引**:
- `idx_DocumentLink_sourceDocPath` ON (`sourceDocPath`)
- `idx_DocumentLink_targetDocPath` ON (`targetDocPath`)
- `idx_DocumentLink_sourceDocPath_targetDocPath` ON (`sourceDocPath`, `targetDocPath`)

### 6. 其他功能类

#### Checklist 表
```sql
CREATE TABLE IF NOT EXISTS "Checklist" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "template" TEXT NOT NULL,  -- JSON格式存储清单模板
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "areaId" TEXT NOT NULL,
  FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，清单唯一标识符
- `name`: 清单名称
- `template`: 清单模板 (JSON格式)
- `createdAt`: 创建时间
- `areaId`: 关联领域ID，外键

**外键关系**:
- `areaId` → `Area.id` (CASCADE ON DELETE)

#### ChecklistInstance 表
```sql
CREATE TABLE IF NOT EXISTS "ChecklistInstance" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "status" TEXT NOT NULL,  -- JSON格式存储清单状态
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "completedAt" TEXT,
  "checklistId" TEXT NOT NULL,
  FOREIGN KEY ("checklistId") REFERENCES "Checklist" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，清单实例唯一标识符
- `status`: 清单状态 (JSON格式，记录每项的完成状态)
- `createdAt`: 创建时间
- `completedAt`: 完成时间
- `checklistId`: 关联清单模板ID，外键

**外键关系**:
- `checklistId` → `Checklist.id` (CASCADE ON DELETE)

#### ReviewTemplate 表
```sql
CREATE TABLE IF NOT EXISTS "ReviewTemplate" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "type" TEXT NOT NULL,
  "structure" TEXT NOT NULL,  -- JSON格式存储模板结构
  "isDefault" INTEGER NOT NULL DEFAULT 0,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明**:
- `id`: 主键，模板唯一标识符
- `name`: 模板名称
- `description`: 模板描述
- `type`: 模板类型 ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')
- `structure`: 模板结构 (JSON格式)
- `isDefault`: 是否默认模板 (0=否, 1=是)
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

#### Review 表
```sql
CREATE TABLE IF NOT EXISTS "Review" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "type" TEXT NOT NULL,
  "period" TEXT NOT NULL,
  "title" TEXT,
  "content" TEXT NOT NULL,  -- JSON格式存储回顾内容
  "status" TEXT NOT NULL DEFAULT 'draft',
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "completedAt" TEXT,
  "templateId" TEXT,
  FOREIGN KEY ("templateId") REFERENCES "ReviewTemplate" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);
```

**字段说明**:
- `id`: 主键，回顾唯一标识符
- `type`: 回顾类型 ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')
- `period`: 回顾周期 (如 '2024-01', '2024-W01')
- `title`: 回顾标题
- `content`: 回顾内容 (JSON格式)
- `status`: 状态 ('draft', 'in_progress', 'completed')
- `createdAt`: 创建时间
- `updatedAt`: 更新时间
- `completedAt`: 完成时间
- `templateId`: 关联模板ID，外键

**外键关系**:
- `templateId` → `ReviewTemplate.id` (SET NULL ON DELETE)

#### InboxNote 表
```sql
CREATE TABLE IF NOT EXISTS "InboxNote" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "title" TEXT,
  "content" TEXT NOT NULL,
  "isDaily" INTEGER NOT NULL DEFAULT 0,
  "processed" INTEGER NOT NULL DEFAULT 0,
  "createdAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**字段说明**:
- `id`: 主键，收件箱笔记唯一标识符
- `title`: 笔记标题
- `content`: 笔记内容
- `isDaily`: 是否每日笔记 (0=否, 1=是)
- `processed`: 是否已处理 (0=未处理, 1=已处理)
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

## 数据库关系图

### 核心实体关系

```
User (用户)
├── settings (JSON配置)

Area (领域)
├── Project (项目) [1:N]
├── AreaMetric (领域指标) [1:N]
│   └── AreaMetricRecord (指标记录) [1:N]
├── Habit (习惯) [1:N]
│   └── HabitRecord (习惯记录) [1:N]
├── RecurringTask (定期任务) [1:N]
├── Checklist (清单模板) [1:N]
│   └── ChecklistInstance (清单实例) [1:N]
├── Task (任务) [1:N]
└── ResourceLink (资源链接) [1:N]

Project (项目)
├── ProjectKPI (项目KPI) [1:N]
│   └── KPIRecord (KPI记录) [1:N]
├── Deliverable (交付物) [1:N]
│   └── DeliverableResource (交付物资源) [1:N]
├── Task (任务) [1:N]
└── ResourceLink (资源链接) [1:N]

Task (任务)
├── Task (子任务) [1:N] - 自关联
├── TaskTag (任务标签) [N:M]
│   └── Tag (标签)
└── ResourceLink (资源链接) [1:1] - 可选

DocumentLink (文档链接)
├── sourceDocPath → 文档文件
└── targetDocPath → 文档文件

Review (回顾)
└── ReviewTemplate (回顾模板) [N:1] - 可选

InboxNote (收件箱笔记)
└── 独立实体，无外键关联
```

### 关键关联关系说明

#### 1. 领域-项目关系 (Area ↔ Project)
- **关系类型**: 一对多 (1:N)
- **外键**: `Project.areaId` → `Area.id`
- **删除策略**: SET NULL (删除领域时项目保留，但解除关联)
- **业务含义**: 一个领域可以包含多个项目，项目可以不属于任何领域

#### 2. 任务层级关系 (Task ↔ Task)
- **关系类型**: 自关联一对多 (1:N)
- **外键**: `Task.parentId` → `Task.id`
- **删除策略**: SET NULL (删除父任务时子任务保留)
- **业务含义**: 支持无限层级的任务嵌套结构

#### 3. 任务-项目/领域关系
- **Project关系**: `Task.projectId` → `Project.id` (SET NULL)
- **Area关系**: `Task.areaId` → `Area.id` (SET NULL)
- **业务规则**: 任务可以同时关联项目和领域，也可以都不关联

#### 4. KPI系统关系
- **项目KPI**: `ProjectKPI.projectId` → `Project.id` (CASCADE)
- **领域指标**: `AreaMetric.areaId` → `Area.id` (CASCADE)
- **记录关系**: KPI/Metric → Record (一对多，CASCADE删除)

#### 5. 资源关联关系
- **项目资源**: `ResourceLink.projectId` → `Project.id` (CASCADE)
- **领域资源**: `ResourceLink.areaId` → `Area.id` (CASCADE)
- **任务资源**: `Task.resourceLinkId` → `ResourceLink.id` (SET NULL)

#### 6. 双向链接关系
- **文档链接**: `DocumentLink` 记录文档间的引用关系
- **无外键约束**: 通过文件路径字符串关联
- **支持类型**: WikiLink、引用、提及等多种链接类型

## 数据库设计特点

### 1. 灵活的关联设计
- **可选关联**: 大部分外键允许NULL，提供灵活性
- **多重关联**: 任务可以同时关联项目和领域
- **层级支持**: 任务支持无限层级嵌套

### 2. JSON字段的使用
- **配置存储**: User.settings, 模板结构等
- **动态数据**: 清单状态、回顾内容等
- **扩展性**: 便于添加新字段而不修改表结构

### 3. 时间戳管理
- **创建时间**: 所有主要实体都有createdAt
- **更新时间**: 支持修改的实体有updatedAt
- **完成时间**: 任务、回顾等有completedAt

### 4. 软删除和归档
- **归档标记**: Project.archived, Area.archived
- **保留数据**: 使用标记而非物理删除
- **数据完整性**: 归档后仍保持关联关系

### 5. 性能优化
- **关键索引**: 查询频繁的字段建立索引
- **复合索引**: 多字段组合查询优化
- **外键索引**: 自动为外键创建索引

## 查询优化策略

### 1. 常用查询模式
```sql
-- 获取项目及其任务
SELECT p.*, t.* FROM Project p
LEFT JOIN Task t ON p.id = t.projectId
WHERE p.archived = 0;

-- 获取领域的习惯完成情况
SELECT h.*, hr.* FROM Habit h
LEFT JOIN HabitRecord hr ON h.id = hr.habitId
WHERE h.areaId = ? AND hr.date >= ?;

-- 获取文档的双向链接
SELECT * FROM DocumentLink
WHERE sourceDocPath = ? OR targetDocPath = ?;
```

### 2. 索引使用建议
- **时间范围查询**: 在时间字段上建立索引
- **状态筛选**: 在status、completed等字段建立索引
- **关联查询**: 外键字段自动有索引
- **复合查询**: 多字段组合查询建立复合索引

### 3. 数据分页策略
- **大表分页**: 使用LIMIT和OFFSET
- **时间分页**: 基于时间戳的游标分页
- **层级分页**: 任务层级的递归查询优化

## 数据完整性保证

### 1. 外键约束
- **级联删除**: 子记录随父记录删除
- **置空处理**: 可选关联在父记录删除时置空
- **更新级联**: 主键更新时自动更新外键

### 2. 唯一性约束
- **用户名唯一**: User.username
- **标签名唯一**: Tag.name
- **习惯日期唯一**: (Habit.id, HabitRecord.date)

### 3. 业务规则验证
- **应用层验证**: 复杂业务规则在应用层实现
- **数据库约束**: 基本约束在数据库层实现
- **事务保证**: 相关操作使用事务确保一致性

## 总结

PaoLife的数据库设计具有以下特点：

1. **完整性**: 覆盖P.A.R.A.方法论的所有核心概念
2. **灵活性**: 支持可选关联和多重关联
3. **扩展性**: 使用JSON字段支持动态扩展
4. **性能**: 合理的索引设计和查询优化
5. **一致性**: 完善的外键约束和业务规则

这个数据库设计为PaoLife应用提供了坚实的数据基础，支持复杂的业务逻辑和高效的数据操作。
