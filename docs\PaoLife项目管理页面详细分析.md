# PaoLife项目详细技术文档

## 第四部分：项目管理页面详细分析

### 页面概览

项目管理模块是PaoLife应用的核心功能之一，包含项目列表页面 (`ProjectsPage.tsx`) 和项目详情页面 (`ProjectDetailPage.tsx`)。实现了完整的项目生命周期管理，包括项目创建、KPI追踪、交付物管理、任务关联和资源链接等功能。

### 核心架构设计

#### 1. 项目列表页面 (ProjectsPage.tsx)

**页面布局结构**:
```typescript
// 三种视图模式
const ViewModes = {
  GRID: 'grid',     // 网格视图 (默认)
  LIST: 'list',     // 列表视图
  KANBAN: 'kanban'  // 看板视图
}

// 页面状态管理
const [viewMode, setViewMode] = useState<'grid' | 'list' | 'kanban'>('grid')
const [searchQuery, setSearchQuery] = useState('')
const [statusFilter, setStatusFilter] = useState('all')
const [areaFilter, setAreaFilter] = useState('all')
const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
const [editingProject, setEditingProject] = useState<Project | null>(null)
```

**项目筛选和搜索**:
```typescript
// 高级筛选逻辑
const filteredProjects = useMemo(() => {
  return projects
    .filter(project => !project.archived) // 排除归档项目
    .filter(project => {
      // 搜索过滤
      const matchesSearch = !searchQuery || 
        project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description?.toLowerCase().includes(searchQuery.toLowerCase())

      // 状态过滤
      const matchesStatus = statusFilter === 'all' || project.status === statusFilter

      // 领域过滤
      const matchesArea = areaFilter === 'all' || 
        (areaFilter === 'unassigned' && !project.areaId) ||
        project.areaId === areaFilter

      return matchesSearch && matchesStatus && matchesArea
    })
    .sort((a, b) => {
      // 排序优先级: 进行中 > 计划中 > 暂停 > 完成
      const statusPriority = {
        'In Progress': 1,
        'Not Started': 2,
        'At Risk': 3,
        'Paused': 4,
        'Completed': 5
      }
      
      const aPriority = statusPriority[a.status] || 999
      const bPriority = statusPriority[b.status] || 999
      
      if (aPriority !== bPriority) {
        return aPriority - bPriority
      }
      
      // 相同状态按更新时间倒序
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    })
}, [projects, searchQuery, statusFilter, areaFilter])
```

**看板视图实现**:
```typescript
// 看板数据结构
const kanbanData = useMemo(() => {
  const columns = [
    { key: 'Not Started', label: '未开始', projects: [] },
    { key: 'In Progress', label: '进行中', projects: [] },
    { key: 'At Risk', label: '有风险', projects: [] },
    { key: 'Completed', label: '已完成', projects: [] }
  ]

  // 将项目分配到对应列
  filteredProjects.forEach(project => {
    const column = columns.find(col => col.key === project.status)
    if (column) {
      column.projects.push(project)
    }
  })

  return columns
}, [filteredProjects])

// 看板列组件
const KanbanColumn = ({ title, status, projects, onEdit, onDelete, onArchive }) => (
  <div className="flex flex-col h-full">
    <div className="flex items-center justify-between p-4 border-b">
      <h3 className="font-semibold">{title}</h3>
      <Badge variant="secondary">{projects.length}</Badge>
    </div>
    <div className="flex-1 overflow-y-auto p-4 space-y-3">
      {projects.map(project => (
        <KanbanProjectCard
          key={project.id}
          project={project}
          onEdit={onEdit}
          onDelete={onDelete}
          onArchive={onArchive}
        />
      ))}
    </div>
  </div>
)
```

#### 2. 项目卡片组件 (ProjectCard.tsx)

**卡片信息展示**:
```typescript
// 项目进度计算
const calculateProgress = (project: Project) => {
  const projectTasks = tasks.filter(task => task.projectId === project.id)
  if (projectTasks.length === 0) return 0
  
  const completedTasks = projectTasks.filter(task => task.completed)
  return Math.round((completedTasks.length / projectTasks.length) * 100)
}

// 截止日期计算
const getDaysUntilDeadline = (deadline: string) => {
  if (!deadline) return null
  const now = new Date()
  const deadlineDate = new Date(deadline)
  const diffTime = deadlineDate.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

// 状态颜色映射
const getStatusColor = (status: string) => {
  const colors = {
    'Not Started': 'bg-gray-100 text-gray-700',
    'In Progress': 'bg-blue-100 text-blue-700',
    'At Risk': 'bg-red-100 text-red-700',
    'Paused': 'bg-yellow-100 text-yellow-700',
    'Completed': 'bg-green-100 text-green-700'
  }
  return colors[status] || 'bg-gray-100 text-gray-700'
}
```

**卡片交互功能**:
```typescript
// 项目卡片组件
const ProjectCard = ({ project, onEdit, onDelete, onArchive, fromArea }) => (
  <Card className="group transition-all duration-200 hover:shadow-md">
    <CardHeader className="pb-3">
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <CardTitle className="text-lg truncate">
              <Link
                to={`/projects/${project.id}`}
                state={fromArea ? { 
                  from: 'area', 
                  areaId: fromArea.areaId, 
                  areaName: fromArea.areaName 
                } : undefined}
                className="hover:text-primary transition-colors"
              >
                {project.name}
              </Link>
            </CardTitle>
            <Badge variant="outline" className={getStatusColor(project.status)}>
              {getStatusText(project.status)}
            </Badge>
            {/* 领域标签 */}
            {associatedArea && (
              <Link
                to={`/areas/${associatedArea.id}`}
                className="inline-flex items-center px-2 py-1 rounded-full border text-xs hover:bg-accent transition-colors"
              >
                {associatedArea.name}
              </Link>
            )}
          </div>
          {project.description && (
            <CardDescription className="text-sm line-clamp-2">
              {project.description}
            </CardDescription>
          )}
        </div>
        
        {/* 操作菜单 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onEdit(project)}>
              <Edit className="h-4 w-4 mr-2" />
              编辑
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onArchive(project)}>
              <Archive className="h-4 w-4 mr-2" />
              归档
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onDelete(project)}
              className="text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </CardHeader>

    <CardContent className="pt-0">
      {/* 进度条 */}
      <div className="space-y-2 mb-4">
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">进度</span>
          <span className="font-medium">{calculateProgress(project)}%</span>
        </div>
        <Progress value={calculateProgress(project)} className="h-2" />
      </div>

      {/* 截止日期 */}
      {project.deadline && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Calendar className="h-4 w-4" />
          <span>
            {getDaysUntilDeadline(project.deadline) > 0 
              ? `还有 ${getDaysUntilDeadline(project.deadline)} 天`
              : getDaysUntilDeadline(project.deadline) === 0
              ? '今天到期'
              : `逾期 ${Math.abs(getDaysUntilDeadline(project.deadline))} 天`
            }
          </span>
        </div>
      )}

      {/* 任务统计 */}
      <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
        <div className="flex items-center gap-1">
          <CheckSquare className="h-4 w-4" />
          <span>{tasks.filter(t => t.projectId === project.id && t.completed).length}</span>
        </div>
        <div className="flex items-center gap-1">
          <Square className="h-4 w-4" />
          <span>{tasks.filter(t => t.projectId === project.id && !t.completed).length}</span>
        </div>
      </div>
    </CardContent>
  </Card>
)
```

#### 3. 项目详情页面 (ProjectDetailPage.tsx)

**页面结构布局**:
```typescript
// 两栏布局设计
const ProjectDetailLayout = () => (
  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
    {/* 左侧主要内容 (2/3宽度) */}
    <div className="lg:col-span-2 space-y-6">
      {/* 项目信息卡片 */}
      <ProjectInfoCard project={project} />
      
      {/* 任务列表 */}
      <ProjectTasksCard projectId={project.id} />
    </div>

    {/* 右侧辅助内容 (1/3宽度) */}
    <div className="space-y-6">
      {/* KPI管理 */}
      <ProjectKPIManagement projectId={project.id} />
      
      {/* 交付物管理 */}
      <ProjectDeliverables projectId={project.id} />
      
      {/* 资源管理 */}
      <ProjectResources projectId={project.id} />
    </div>
  </div>
)
```

**项目数据加载策略**:
```typescript
// 支持归档项目的数据加载
const loadProjectData = async () => {
  setIsLoadingProject(true)
  try {
    if (isArchived) {
      // 归档项目通过API直接获取
      const result = await databaseApi.getProjectById(projectId, true)
      if (result.success && result.data) {
        setProjectData(result.data)
      } else {
        addNotification({
          type: 'error',
          title: '项目未找到',
          message: '未找到该项目，可能已被删除。'
        })
        navigate('/archive')
      }
    } else {
      // 活跃项目从store获取
      const project = projects.find(p => p.id === projectId)
      if (project) {
        setProjectData(project)
      } else {
        // store中没有则通过API获取
        const result = await databaseApi.getProjectById(projectId, false)
        if (result.success && result.data) {
          setProjectData(result.data)
        } else {
          addNotification({
            type: 'error',
            title: '项目未找到',
            message: '未找到该项目，可能已被删除。'
          })
          navigate('/projects')
        }
      }
    }
  } catch (error) {
    console.error('Failed to load project:', error)
    addNotification({
      type: 'error',
      title: '加载失败',
      message: '项目数据加载失败，请重试。'
    })
  } finally {
    setIsLoadingProject(false)
  }
}
```

**项目进度自动计算**:
```typescript
// 基于任务完成情况自动更新项目进度
const updateProjectProgress = useCallback(() => {
  if (!project) return

  const projectTasks = tasks.filter(task => task.projectId === project.id)
  if (projectTasks.length === 0) return

  const completedTasks = projectTasks.filter(task => task.completed)
  const progressPercentage = Math.round((completedTasks.length / projectTasks.length) * 100)

  // 防抖更新，避免频繁数据库操作
  const debouncedUpdate = debounce(() => {
    updateProject(project.id, {
      progress: progressPercentage,
      updatedAt: new Date()
    })
  }, 100)

  debouncedUpdate()
}, [project, tasks, updateProject])

// 监听任务变化自动更新进度
useEffect(() => {
  updateProjectProgress()
}, [tasks, updateProjectProgress])
```

#### 4. 项目KPI管理系统

**统一KPI管理架构**:
```typescript
// KPI管理器接口
interface KPIApiInterface<T, R> {
  create(data: CreateKPIData & { parentId: string }): Promise<T>
  update(id: string, data: Partial<CreateKPIData>): Promise<T>
  delete(id: string): Promise<void>
  getAll(parentId: string): Promise<T[]>
  createRecord(kpiId: string, data: CreateKPIRecordData): Promise<R>
  getRecords(kpiId: string, options?: GetRecordsOptions): Promise<R[]>
  getStatistics(parentId: string): Promise<KPIStatistics>
}

// 项目KPI API适配器
class ProjectKPIApiAdapter implements KPIApiInterface<ProjectKPIExtended, ProjectKPIRecord> {
  async create(data: CreateKPIData & { parentId: string }): Promise<ProjectKPIExtended> {
    const result = await databaseApi.createProjectKPI({
      projectId: data.parentId,
      name: data.name,
      value: data.value,
      target: data.target,
      unit: data.unit,
      frequency: data.frequency,
      direction: data.direction
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to create project KPI')
    }

    return {
      ...result.data,
      projectId: data.parentId
    }
  }

  async update(id: string, data: Partial<CreateKPIData>): Promise<ProjectKPIExtended> {
    const result = await databaseApi.updateProjectKPI({ id, updates: data })
    if (!result.success) {
      throw new Error(result.error || 'Failed to update project KPI')
    }
    return result.data
  }

  async getStatistics(projectId: string): Promise<KPIStatistics> {
    const result = await databaseApi.getProjectKPIStatistics(projectId)
    if (!result.success) {
      throw new Error(result.error || 'Failed to get KPI statistics')
    }
    return result.data
  }
}
```

**KPI组件实现**:
```typescript
// ProjectKPIManagement组件
const ProjectKPIManagement = ({ projectId }) => {
  const [kpis, setKpis] = useState<ProjectKPI[]>([])
  const [statistics, setStatistics] = useState<KPIStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingKPI, setEditingKPI] = useState<ProjectKPI | null>(null)

  // 使用统一的KPI管理器
  const kpiManager = createProjectKPIManager()

  // 加载KPI数据
  const loadKPIs = async () => {
    try {
      setLoading(true)
      const [kpisData, statsData] = await Promise.all([
        kpiManager.getAll(projectId),
        kpiManager.getStatistics(projectId)
      ])
      setKpis(kpisData)
      setStatistics(statsData)
    } catch (error) {
      console.error('Failed to load KPIs:', error)
      addNotification({
        type: 'error',
        title: 'Failed to load KPIs',
        message: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  // 创建KPI
  const handleCreateKPI = async (data: CreateKPIData) => {
    try {
      const newKPI = await kpiManager.create(projectId, data)
      setKpis(prev => [newKPI, ...prev])
      setShowCreateDialog(false)
      await loadKPIs() // 重新加载统计数据

      addNotification({
        type: 'success',
        title: 'KPI Created',
        message: `KPI "${data.name}" has been created successfully`
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Failed to create KPI',
        message: error.message
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              项目KPI
            </CardTitle>
            <CardDescription>
              跟踪项目关键绩效指标
            </CardDescription>
          </div>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            添加KPI
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {/* KPI统计概览 */}
        {statistics && (
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="text-center p-3 bg-accent/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {statistics.onTrackCount}
              </div>
              <div className="text-sm text-muted-foreground">达标KPI</div>
            </div>
            <div className="text-center p-3 bg-accent/20 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {statistics.offTrackCount}
              </div>
              <div className="text-sm text-muted-foreground">未达标KPI</div>
            </div>
          </div>
        )}

        {/* KPI列表 */}
        {kpis.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">📊</div>
            <p className="text-sm">暂无KPI指标</p>
            <p className="text-xs mt-1">添加KPI来跟踪项目表现</p>
          </div>
        ) : (
          <div className="space-y-3">
            {kpis.map(kpi => (
              <KPIItem
                key={kpi.id}
                kpi={kpi}
                onEdit={setEditingKPI}
                onDelete={handleDeleteKPI}
                onRecord={handleRecordKPI}
              />
            ))}
          </div>
        )}
      </CardContent>

      {/* KPI对话框 */}
      <UniversalKPIDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSubmit={handleCreateKPI}
        type="project"
        entityId={projectId}
      />
    </Card>
  )
}
```

#### 5. 交付物管理系统

**交付物数据结构**:
```typescript
interface Deliverable {
  id: string
  title: string
  description?: string
  type: 'document' | 'software' | 'presentation' | 'report'
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled'
  content?: string
  url?: string
  filePath?: string
  acceptanceCriteria?: string  // JSON格式存储验收标准
  plannedDate?: string
  actualDate?: string
  projectId: string
  createdAt: string
  updatedAt: string
}
```

**交付物管理组件**:
```typescript
const ProjectDeliverables = ({ projectId }) => {
  const [deliverables, setDeliverables] = useState<Deliverable[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  // 加载交付物
  const loadDeliverables = async () => {
    try {
      setLoading(true)
      const result = await databaseApi.getProjectDeliverables(projectId)
      if (result.success) {
        setDeliverables(result.data || [])
      }
    } catch (error) {
      console.error('Failed to load deliverables:', error)
      addNotification({
        type: 'error',
        title: 'Failed to load deliverables',
        message: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  // 创建交付物
  const handleCreateDeliverable = async (data: CreateDeliverableData) => {
    try {
      const result = await databaseApi.createDeliverable({
        ...data,
        projectId
      })
      if (result.success) {
        setDeliverables(prev => [result.data, ...prev])
        addNotification({
          type: 'success',
          title: 'Deliverable created',
          message: `"${data.title}" has been created successfully`
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Failed to create deliverable',
        message: error.message
      })
    }
  }

  // 更新交付物状态
  const handleUpdateStatus = async (deliverable: Deliverable, newStatus: string) => {
    try {
      const updates: any = { status: newStatus }

      // 完成时设置实际完成日期
      if (newStatus === 'completed' && deliverable.status !== 'completed') {
        updates.actualDate = new Date().toISOString()
      }

      const result = await databaseApi.updateDeliverable({
        id: deliverable.id,
        updates
      })

      if (result.success) {
        setDeliverables(prev =>
          prev.map(d => d.id === deliverable.id ? result.data : d)
        )
        addNotification({
          type: 'success',
          title: 'Status updated',
          message: `"${deliverable.title}" status updated to ${newStatus}`
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Failed to update status',
        message: error.message
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>项目交付物</CardTitle>
            <CardDescription>管理项目的可交付成果</CardDescription>
          </div>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            添加交付物
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {deliverables.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">🎯</div>
            <p className="text-sm">暂无交付物</p>
            <p className="text-xs mt-1">添加项目的可交付成果</p>
          </div>
        ) : (
          <div className="space-y-3">
            {deliverables.map(deliverable => (
              <DeliverableItem
                key={deliverable.id}
                deliverable={deliverable}
                onUpdateStatus={handleUpdateStatus}
                onEdit={handleEditDeliverable}
                onDelete={handleDeleteDeliverable}
              />
            ))}
          </div>
        )}
      </CardContent>

      {/* 创建交付物对话框 */}
      <CreateDeliverableDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSubmit={handleCreateDeliverable}
      />
    </Card>
  )
}
```

#### 6. 项目任务管理系统

**任务列表组件架构**:
```typescript
// ProjectTasksCard - 项目任务管理的核心组件
const ProjectTasksCard = ({ projectId, isArchived = false }) => {
  const { tasks, createTask, updateTask, deleteTask, moveTask } = useTaskStore()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [taskToDelete, setTaskToDelete] = useState<Task | null>(null)

  // 获取项目任务
  const projectTasks = useMemo(() => {
    return tasks
      .filter(task => task.projectId === projectId)
      .sort((a, b) => {
        // 排序逻辑: 未完成优先，然后按position排序
        if (a.completed !== b.completed) {
          return a.completed ? 1 : -1
        }
        return (a.position || 0) - (b.position || 0)
      })
  }, [tasks, projectId])

  // 任务统计
  const taskStats = useMemo(() => {
    const total = projectTasks.length
    const completed = projectTasks.filter(t => t.completed).length
    const pending = total - completed
    const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0

    return { total, completed, pending, completionRate }
  }, [projectTasks])

  // 创建任务
  const handleCreateTask = async (taskData: CreateTaskData) => {
    try {
      const newTask = await createTask({
        ...taskData,
        projectId,
        position: projectTasks.length // 添加到末尾
      })

      setIsCreateDialogOpen(false)
      addNotification({
        type: 'success',
        title: '任务已创建',
        message: `任务 "${taskData.content}" 已成功创建`
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: '创建失败',
        message: error.message
      })
    }
  }

  // 任务完成切换
  const handleTaskToggle = async (taskId: string, completed: boolean) => {
    try {
      await updateTask(taskId, {
        completed,
        completedAt: completed ? new Date().toISOString() : null
      })

      // 触发项目进度更新
      updateProjectProgress()
    } catch (error) {
      addNotification({
        type: 'error',
        title: '更新失败',
        message: error.message
      })
    }
  }

  // 任务拖拽排序
  const handleTaskMove = async (taskId: string, newPosition: number) => {
    try {
      await moveTask(taskId, newPosition)
    } catch (error) {
      addNotification({
        type: 'error',
        title: '移动失败',
        message: error.message
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <CheckSquare className="h-5 w-5" />
              项目任务
              {taskStats.total > 0 && (
                <Badge variant="secondary">
                  {taskStats.completed}/{taskStats.total}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              管理项目的任务和子任务
            </CardDescription>
          </div>
          {!isArchived && (
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              添加任务
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {/* 任务统计 */}
        {taskStats.total > 0 && (
          <div className="mb-6">
            <div className="flex justify-between text-sm mb-2">
              <span className="text-muted-foreground">完成进度</span>
              <span className="font-medium">{taskStats.completionRate}%</span>
            </div>
            <Progress value={taskStats.completionRate} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>已完成: {taskStats.completed}</span>
              <span>待完成: {taskStats.pending}</span>
            </div>
          </div>
        )}

        {/* 任务列表 */}
        {projectTasks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">✅</div>
            <p className="text-sm">暂无任务</p>
            <p className="text-xs mt-1">添加任务来跟踪项目进展</p>
          </div>
        ) : (
          <TaskList
            tasks={projectTasks}
            onTaskToggle={handleTaskToggle}
            onTaskEdit={handleTaskEdit}
            onTaskDelete={handleTaskDelete}
            onTaskMove={handleTaskMove}
            onTaskSelect={setSelectedTaskId}
            selectedTaskId={selectedTaskId}
            showHierarchyControls={!isArchived}
            useVirtualizedView={projectTasks.length > 50}
            className="space-y-2"
          />
        )}
      </CardContent>

      {/* 创建任务对话框 */}
      <CreateTaskDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSubmit={handleCreateTask}
        defaultProjectId={projectId}
      />

      {/* 删除任务对话框 */}
      <DeleteTaskDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        task={taskToDelete}
        onConfirm={handleConfirmDelete}
      />
    </Card>
  )
}
```

**任务层级管理**:
```typescript
// 无限层级任务系统
const TaskHierarchyManager = {
  // 获取任务的所有子任务
  getTaskChildren: (taskId: string, allTasks: Task[]): Task[] => {
    return allTasks.filter(task => task.parentId === taskId)
  },

  // 获取任务的所有后代任务
  getTaskDescendants: (taskId: string, allTasks: Task[]): Task[] => {
    const descendants: Task[] = []
    const children = this.getTaskChildren(taskId, allTasks)

    for (const child of children) {
      descendants.push(child)
      descendants.push(...this.getTaskDescendants(child.id, allTasks))
    }

    return descendants
  },

  // 获取任务层级深度
  getTaskDepth: (taskId: string, allTasks: Task[]): number => {
    const task = allTasks.find(t => t.id === taskId)
    if (!task || !task.parentId) return 0

    return 1 + this.getTaskDepth(task.parentId, allTasks)
  },

  // 检查是否可以移动任务到指定父任务
  canMoveTask: (taskId: string, newParentId: string | null, allTasks: Task[]): boolean => {
    if (!newParentId) return true // 可以移动到根级别

    // 不能移动到自己
    if (taskId === newParentId) return false

    // 不能移动到自己的后代任务
    const descendants = this.getTaskDescendants(taskId, allTasks)
    return !descendants.some(desc => desc.id === newParentId)
  },

  // 计算任务完成率（包含子任务）
  calculateCompletionRate: (taskId: string, allTasks: Task[]): number => {
    const descendants = this.getTaskDescendants(taskId, allTasks)
    if (descendants.length === 0) {
      const task = allTasks.find(t => t.id === taskId)
      return task?.completed ? 100 : 0
    }

    const completedCount = descendants.filter(t => t.completed).length
    return Math.round((completedCount / descendants.length) * 100)
  }
}
```

**任务详情面板**:
```typescript
// TaskDetailPanel - 右侧滑出式任务详情面板
const TaskDetailPanel = ({ taskId, isOpen, onClose }) => {
  const { tasks, updateTask, deleteTask } = useTaskStore()
  const [isEditing, setIsEditing] = useState(false)
  const [editData, setEditData] = useState<Partial<Task>>({})

  const task = tasks.find(t => t.id === taskId)
  if (!task) return null

  // 获取子任务
  const childTasks = tasks.filter(t => t.parentId === taskId)

  // 获取父任务
  const parentTask = task.parentId ? tasks.find(t => t.id === task.parentId) : null

  // 保存编辑
  const handleSaveEdit = async () => {
    try {
      await updateTask(taskId, {
        ...editData,
        updatedAt: new Date().toISOString()
      })
      setIsEditing(false)
      setEditData({})

      addNotification({
        type: 'success',
        title: '任务已更新',
        message: '任务信息已成功更新'
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: '更新失败',
        message: error.message
      })
    }
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5" />
            任务详情
          </SheetTitle>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          {/* 任务基本信息 */}
          <div className="space-y-4">
            {isEditing ? (
              <div className="space-y-3">
                <div>
                  <Label htmlFor="content">任务内容</Label>
                  <Input
                    id="content"
                    value={editData.content || task.content}
                    onChange={(e) => setEditData(prev => ({ ...prev, content: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="description">任务描述</Label>
                  <Textarea
                    id="description"
                    value={editData.description || task.description || ''}
                    onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleSaveEdit}>保存</Button>
                  <Button variant="outline" onClick={() => setIsEditing(false)}>
                    取消
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div>
                  <Label>任务内容</Label>
                  <p className="text-sm mt-1">{task.content}</p>
                </div>
                {task.description && (
                  <div>
                    <Label>任务描述</Label>
                    <p className="text-sm mt-1 text-muted-foreground">{task.description}</p>
                  </div>
                )}
                <Button variant="outline" onClick={() => setIsEditing(true)}>
                  <Edit className="h-4 w-4 mr-2" />
                  编辑
                </Button>
              </div>
            )}
          </div>

          {/* 任务状态 */}
          <div className="space-y-3">
            <Label>任务状态</Label>
            <div className="flex items-center gap-3">
              <Checkbox
                checked={task.completed}
                onCheckedChange={(checked) => handleTaskToggle(taskId, checked)}
              />
              <span className={cn(
                "text-sm",
                task.completed && "line-through text-muted-foreground"
              )}>
                {task.completed ? '已完成' : '待完成'}
              </span>
            </div>
            {task.completedAt && (
              <p className="text-xs text-muted-foreground">
                完成时间: {new Date(task.completedAt).toLocaleString()}
              </p>
            )}
          </div>

          {/* 任务层级信息 */}
          {parentTask && (
            <div className="space-y-2">
              <Label>父任务</Label>
              <div className="p-2 border rounded-lg">
                <p className="text-sm">{parentTask.content}</p>
              </div>
            </div>
          )}

          {/* 子任务列表 */}
          {childTasks.length > 0 && (
            <div className="space-y-3">
              <Label>子任务 ({childTasks.length})</Label>
              <div className="space-y-2">
                {childTasks.map(childTask => (
                  <div key={childTask.id} className="flex items-center gap-2 p-2 border rounded-lg">
                    <Checkbox
                      checked={childTask.completed}
                      onCheckedChange={(checked) => handleTaskToggle(childTask.id, checked)}
                    />
                    <span className={cn(
                      "text-sm flex-1",
                      childTask.completed && "line-through text-muted-foreground"
                    )}>
                      {childTask.content}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 任务时间信息 */}
          <div className="space-y-2">
            <Label>时间信息</Label>
            <div className="text-sm text-muted-foreground space-y-1">
              <p>创建时间: {new Date(task.createdAt).toLocaleString()}</p>
              <p>更新时间: {new Date(task.updatedAt).toLocaleString()}</p>
              {task.dueDate && (
                <p>截止时间: {new Date(task.dueDate).toLocaleString()}</p>
              )}
            </div>
          </div>

          {/* 删除任务 */}
          <div className="pt-4 border-t">
            <Button
              variant="destructive"
              onClick={() => handleDeleteTask(task)}
              className="w-full"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              删除任务
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}
```

#### 7. 项目资源管理系统

**统一资源管理组件**:
```typescript
// UnifiedResources组件 - 支持项目和领域的资源管理
const UnifiedResources = ({ projectId, areaId, compact = false }) => {
  const [resources, setResources] = useState<ResourceLink[]>([])
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false)
  const [loading, setLoading] = useState(true)

  // 加载资源
  const loadResources = async () => {
    if (!projectId && !areaId) return

    try {
      setLoading(true)
      const result = projectId
        ? await databaseApi.getProjectResources(projectId)
        : await databaseApi.getAreaResources(areaId!)

      if (result.success) {
        setResources(result.data || [])
      }
    } catch (error) {
      console.error('Error loading resources:', error)
      addNotification({
        type: 'error',
        title: 'Error loading resources',
        message: 'An unexpected error occurred'
      })
    } finally {
      setLoading(false)
    }
  }

  // 链接资源
  const handleLinkResource = async (resourcePath: string, title?: string) => {
    try {
      const result = await databaseApi.createResourceLink({
        resourcePath,
        title: title || extractFileName(resourcePath),
        projectId: projectId || undefined,
        areaId: areaId || undefined
      })

      if (result.success) {
        setResources(prev => [result.data, ...prev])
        addNotification({
          type: 'success',
          title: 'Resource linked',
          message: `"${result.data.title}" has been linked successfully`
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Failed to link resource',
        message: error.message
      })
    }
  }

  // 取消链接资源
  const handleUnlinkResource = async (resourceId: string) => {
    try {
      const result = await databaseApi.deleteResourceLink(resourceId)
      if (result.success) {
        setResources(prev => prev.filter(r => r.id !== resourceId))
        addNotification({
          type: 'success',
          title: 'Resource unlinked',
          message: 'Resource has been unlinked successfully'
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Failed to unlink resource',
        message: error.message
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className={`flex items-center gap-2 ${compact ? 'text-lg' : 'text-xl'}`}>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              {compact ? '关联资源' : '项目资源'}
              {resources.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {resources.length}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              {compact ? '管理相关文档和资源' : '管理项目相关的文档和资源'}
            </CardDescription>
          </div>
          <Button
            onClick={() => setIsLinkDialogOpen(true)}
            size={compact ? "sm" : "default"}
          >
            <Link className="h-4 w-4 mr-2" />
            {compact ? '链接' : '链接资源'}
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {resources.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">📎</div>
            <p className="text-sm">
              {compact ? '暂无关联资源' : '暂无项目资源'}
            </p>
            <p className="text-xs mt-1">
              {compact ? '点击"链接资源"添加相关文档' : '链接相关文档和资源到此项目'}
            </p>
          </div>
        ) : (
          <div className={compact ? "space-y-2" : "space-y-3"}>
            {resources.map((resource) => (
              <ResourceItem
                key={resource.id}
                resource={resource}
                compact={compact}
                onUnlink={handleUnlinkResource}
              />
            ))}
          </div>
        )}
      </CardContent>

      {/* 链接资源对话框 */}
      <LinkResourceDialog
        isOpen={isLinkDialogOpen}
        onClose={() => setIsLinkDialogOpen(false)}
        onSubmit={handleLinkResource}
        entityType={projectId ? 'project' : 'area'}
        entityId={projectId || areaId}
      />
    </Card>
  )
}
```

#### 7. 项目操作和生命周期管理

**项目状态管理**:
```typescript
// 项目状态转换逻辑
const ProjectStatusManager = {
  // 允许的状态转换
  allowedTransitions: {
    'Not Started': ['In Progress', 'Paused', 'Cancelled'],
    'In Progress': ['Completed', 'At Risk', 'Paused'],
    'At Risk': ['In Progress', 'Paused', 'Cancelled'],
    'Paused': ['In Progress', 'Cancelled'],
    'Completed': ['In Progress'], // 允许重新激活
    'Cancelled': ['Not Started'] // 允许重新启动
  },

  // 检查状态转换是否有效
  canTransition(from: string, to: string): boolean {
    return this.allowedTransitions[from]?.includes(to) || false
  },

  // 获取可用的下一状态
  getAvailableTransitions(currentStatus: string): string[] {
    return this.allowedTransitions[currentStatus] || []
  },

  // 状态变更时的自动操作
  async onStatusChange(project: Project, newStatus: string) {
    const updates: Partial<Project> = { status: newStatus }

    switch (newStatus) {
      case 'In Progress':
        if (!project.startDate) {
          updates.startDate = new Date().toISOString()
        }
        break

      case 'Completed':
        updates.progress = 100
        // 自动完成所有未完成任务
        const incompleteTasks = tasks.filter(t => t.projectId === project.id && !t.completed)
        for (const task of incompleteTasks) {
          await updateTask(task.id, { completed: true, completedAt: new Date().toISOString() })
        }
        break

      case 'Cancelled':
        // 可选：暂停所有相关任务
        break
    }

    return updates
  }
}
```

**项目归档和删除**:
```typescript
// 项目归档处理
const handleArchiveProject = async (project: Project) => {
  const confirmed = await confirm({
    title: '确认归档项目',
    message: `确定要归档项目 "${project.name}" 吗？归档后项目将移至归档页面。`,
    confirmText: '归档',
    cancelText: '取消'
  })

  if (confirmed) {
    try {
      await archiveProject(project.id)
      addNotification({
        type: 'success',
        title: '项目已归档',
        message: `项目 "${project.name}" 已成功归档`
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: '归档失败',
        message: error.message
      })
    }
  }
}

// 项目删除处理
const handleDeleteProject = async (project: Project) => {
  const confirmed = await confirm({
    title: '确认删除项目',
    message: `确定要删除项目 "${project.name}" 吗？此操作不可撤销，所有相关数据将被永久删除。`,
    confirmText: '删除',
    cancelText: '取消',
    variant: 'destructive'
  })

  if (confirmed) {
    try {
      await deleteProject(project.id)
      addNotification({
        type: 'success',
        title: '项目已删除',
        message: `项目 "${project.name}" 已成功删除`
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: '删除失败',
        message: error.message
      })
    }
  }
}
```

#### 8. 性能优化和用户体验

**数据加载优化**:
```typescript
// 使用React Query进行数据缓存和同步
const useProjectData = (projectId: string) => {
  return useQuery({
    queryKey: ['project', projectId],
    queryFn: () => databaseApi.getProjectById(projectId),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
    cacheTime: 10 * 60 * 1000, // 10分钟保留
    refetchOnWindowFocus: false
  })
}

// 虚拟化长列表
const VirtualizedProjectList = ({ projects }) => {
  const parentRef = useRef<HTMLDivElement>(null)

  const rowVirtualizer = useVirtualizer({
    count: projects.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 200, // 估计每个项目卡片高度
    overscan: 5
  })

  return (
    <div ref={parentRef} className="h-full overflow-auto">
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative'
        }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualItem) => (
          <div
            key={virtualItem.key}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`
            }}
          >
            <ProjectCard project={projects[virtualItem.index]} />
          </div>
        ))}
      </div>
    </div>
  )
}
```

### 总结

项目管理模块作为PaoLife应用的核心功能，具有以下特点：

1. **完整的生命周期管理**: 从创建到归档的全流程支持
2. **多视图展示**: 网格、列表、看板三种视图模式
3. **智能进度跟踪**: 基于任务完成情况自动计算项目进度
4. **丰富的管理功能**: KPI追踪、交付物管理、资源链接
5. **灵活的状态管理**: 支持复杂的状态转换和自动化操作
6. **优秀的用户体验**: 响应式设计、虚拟化渲染、实时同步

这个设计为用户提供了专业级的项目管理能力，支持复杂项目的全方位管理和跟踪。
