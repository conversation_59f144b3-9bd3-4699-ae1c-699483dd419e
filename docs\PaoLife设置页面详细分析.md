# PaoLife项目详细技术文档

## 第十一部分：设置页面详细分析

### 页面概览

设置页面是PaoLife应用的配置管理中心，实现了应用的全方位个性化配置。包含通用设置、编辑器设置、P.A.R.A.方法论设置、数据库管理、导入导出功能等，为用户提供灵活的应用定制能力。

### 核心架构设计

#### 1. 设置数据结构

**用户设置接口**:
```typescript
export interface UserSettings {
  username: string
  workspaceDirectory: string
  resourcePath?: string // 资源库路径
  isFirstTime: boolean
  lastLoginTime?: string
  editorMode?: 'wysiwyg' | 'ir'
  editorTheme?: 'classic' | 'dark'
  focusMode?: boolean
  autoSave?: boolean
  autoSaveInterval?: number // 自动保存间隔（秒）
  showExitConfirm?: boolean
}

interface PARASettings {
  autoArchive: {
    enabled: boolean
    inactiveDays: number
    completedProjectDays: number
  }
  weeklyReview: {
    enabled: boolean
    dayOfWeek: number // 0-6, 0为周日
    reminderTime: string // HH:MM格式
  }
  projectTemplate: {
    defaultTemplate: string
    autoCreateTasks: boolean
    defaultPriority: string
  }
}
```

**设置存储架构**:
```typescript
interface UserSettingsState {
  settings: UserSettings
  isInitialized: boolean
  updateSettings: (newSettings: Partial<UserSettings>) => void
  setWorkspaceDirectory: (directory: string) => void
  setUsername: (username: string) => void
  setEditorMode: (mode: 'wysiwyg' | 'ir') => void
  setEditorTheme: (theme: 'classic' | 'dark') => void
  setFocusMode: (enabled: boolean) => void
  setAutoSave: (enabled: boolean) => void
  setAutoSaveInterval: (interval: number) => void
  setShowExitConfirm: (enabled: boolean) => void
  resetSettings: () => void
}
```

#### 2. 设置页面组件 (SettingsPage.tsx)

**页面状态管理**:
```typescript
export function SettingsPage() {
  const { language, setLanguage, t } = useLanguage()
  const {
    settings,
    updateSettings,
    setUsername,
    setEditorMode,
    setEditorTheme,
    setFocusMode,
    setAutoSave,
    setAutoSaveInterval,
    setShowExitConfirm,
    resetSettings
  } = useUserSettingsStore()
  const { theme, setTheme, addNotification } = useUIStore()
  const {
    settings: paraSettings,
    updateAutoArchive,
    updateWeeklyReview,
    updateProjectTemplate
  } = usePARASettingsStore()

  // 本地状态用于输入控制
  const [localUsername, setLocalUsername] = useState(settings.username)
  const [localAutoSaveInterval, setLocalAutoSaveInterval] = useState(settings.autoSaveInterval || 30)
  const [databaseInfo, setDatabaseInfo] = useState<any>(null)
  const [shortcutsDialogOpen, setShortcutsDialogOpen] = useState(false)

  // 同步设置变化
  useEffect(() => {
    setLocalUsername(settings.username)
    setLocalAutoSaveInterval(settings.autoSaveInterval || 30)
  }, [settings.username, settings.autoSaveInterval])
}
```

**语言和主题切换**:
```typescript
const handleLanguageChange = (newLanguage: Language) => {
  setLanguage(newLanguage)
  addNotification({
    type: 'success',
    title: t('settings.notifications.languageChanged'),
    message: t('settings.notifications.languageChangedMessage')
  })
}

const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
  setTheme(newTheme)
  addNotification({
    type: 'success',
    title: t('settings.notifications.themeChanged'),
    message: t('settings.notifications.themeChangedMessage')
  })
}
```

#### 3. 通用设置管理

**用户信息设置**:
```typescript
const handleUsernameChange = (value: string) => {
  setLocalUsername(value)
}

const handleUsernameSave = () => {
  if (localUsername.trim() && localUsername !== settings.username) {
    setUsername(localUsername.trim())
    addNotification({
      type: 'success',
      title: t('settings.notifications.usernameSaved'),
      message: t('settings.notifications.usernameSavedMessage')
    })
  }
}

// 工作区目录设置
const handleWorkspaceDirectoryChange = async () => {
  try {
    const result = await window.electronAPI.dialog.showOpenDialog({
      properties: ['openDirectory'],
      title: t('settings.selectWorkspaceDirectory')
    })

    if (!result.canceled && result.filePaths.length > 0) {
      const directory = result.filePaths[0]
      setWorkspaceDirectory(directory)
      addNotification({
        type: 'success',
        title: t('settings.notifications.workspaceChanged'),
        message: t('settings.notifications.workspaceChangedMessage')
      })
    }
  } catch (error) {
    addNotification({
      type: 'error',
      title: t('settings.notifications.workspaceError'),
      message: error.message
    })
  }
}
```

#### 4. 编辑器设置管理

**编辑器配置**:
```typescript
// 编辑器模式切换
const handleEditorModeChange = (mode: 'wysiwyg' | 'ir') => {
  setEditorMode(mode)
  addNotification({
    type: 'success',
    title: t('settings.notifications.editorModeChanged'),
    message: t('settings.notifications.editorModeChangedMessage')
  })
}

// 编辑器主题切换
const handleEditorThemeChange = (theme: 'classic' | 'dark') => {
  setEditorTheme(theme)
  addNotification({
    type: 'success',
    title: t('settings.notifications.editorThemeChanged'),
    message: t('settings.notifications.editorThemeChangedMessage')
  })
}

// 专注模式切换
const handleFocusModeChange = (enabled: boolean) => {
  setFocusMode(enabled)
  addNotification({
    type: 'info',
    title: t('settings.notifications.focusModeChanged'),
    message: enabled 
      ? t('settings.notifications.focusModeEnabled')
      : t('settings.notifications.focusModeDisabled')
  })
}

// 自动保存设置
const handleAutoSaveChange = (enabled: boolean) => {
  setAutoSave(enabled)
  if (enabled && !settings.autoSaveInterval) {
    setAutoSaveInterval(30) // 默认30秒
  }
}

const handleAutoSaveIntervalChange = (interval: number) => {
  setLocalAutoSaveInterval(interval)
}

const handleAutoSaveIntervalSave = () => {
  if (localAutoSaveInterval !== settings.autoSaveInterval) {
    setAutoSaveInterval(localAutoSaveInterval)
    addNotification({
      type: 'success',
      title: t('settings.notifications.autoSaveIntervalChanged'),
      message: t('settings.notifications.autoSaveIntervalChangedMessage', { 
        interval: localAutoSaveInterval 
      })
    })
  }
}
```

#### 5. P.A.R.A.方法论设置

**自动归档配置**:
```typescript
// 自动归档设置
const handleAutoArchiveChange = (config: Partial<PARASettings['autoArchive']>) => {
  updateAutoArchive(config)
  addNotification({
    type: 'success',
    title: t('settings.notifications.paraSettingsChanged'),
    message: t('settings.notifications.autoArchiveConfigChanged')
  })
}

// 周回顾设置
const handleWeeklyReviewChange = (config: Partial<PARASettings['weeklyReview']>) => {
  updateWeeklyReview(config)
  addNotification({
    type: 'success',
    title: t('settings.notifications.paraSettingsChanged'),
    message: t('settings.notifications.weeklyReviewConfigChanged')
  })
}

// 项目模板设置
const handleProjectTemplateChange = (config: Partial<PARASettings['projectTemplate']>) => {
  updateProjectTemplate(config)
  addNotification({
    type: 'success',
    title: t('settings.notifications.paraSettingsChanged'),
    message: t('settings.notifications.projectTemplateConfigChanged')
  })
}
```

#### 6. 数据库管理

**数据库信息显示**:
```typescript
// 加载数据库信息
useEffect(() => {
  const loadDatabaseInfo = async () => {
    try {
      const result = await settingsApi.getDatabaseInfo()
      if (result.success) {
        setDatabaseInfo(result.data)
      }
    } catch (error) {
      console.error('Failed to load database info:', error)
    }
  }
  loadDatabaseInfo()
}, [])

// 数据库备份
const handleDatabaseBackup = async () => {
  try {
    const result = await window.electronAPI.dialog.showSaveDialog({
      title: t('settings.database.backup.selectLocation'),
      defaultPath: `paolife-backup-${new Date().toISOString().split('T')[0]}.db`,
      filters: [
        { name: 'Database files', extensions: ['db'] },
        { name: 'All files', extensions: ['*'] }
      ]
    })

    if (!result.canceled && result.filePath) {
      const backupResult = await settingsApi.backupDatabase(result.filePath)
      if (backupResult.success) {
        addNotification({
          type: 'success',
          title: t('settings.notifications.backupSuccess'),
          message: t('settings.notifications.backupSuccessMessage')
        })
      } else {
        throw new Error(backupResult.error)
      }
    }
  } catch (error) {
    addNotification({
      type: 'error',
      title: t('settings.notifications.backupError'),
      message: error.message
    })
  }
}
```

**数据库恢复**:
```typescript
const handleDatabaseRestore = async () => {
  try {
    const result = await window.electronAPI.dialog.showOpenDialog({
      title: t('settings.database.restore.selectFile'),
      filters: [
        { name: 'Database files', extensions: ['db'] },
        { name: 'All files', extensions: ['*'] }
      ],
      properties: ['openFile']
    })

    if (!result.canceled && result.filePaths.length > 0) {
      const confirmed = await window.electronAPI.dialog.showMessageBox({
        type: 'warning',
        title: t('settings.database.restore.confirmTitle'),
        message: t('settings.database.restore.confirmMessage'),
        buttons: [t('common.cancel'), t('common.confirm')],
        defaultId: 0,
        cancelId: 0
      })

      if (confirmed.response === 1) {
        const restoreResult = await settingsApi.restoreDatabase(result.filePaths[0])
        if (restoreResult.success) {
          addNotification({
            type: 'success',
            title: t('settings.notifications.restoreSuccess'),
            message: t('settings.notifications.restoreSuccessMessage')
          })
          // 建议重启应用
          setTimeout(() => {
            window.electronAPI.app.restart()
          }, 2000)
        } else {
          throw new Error(restoreResult.error)
        }
      }
    }
  } catch (error) {
    addNotification({
      type: 'error',
      title: t('settings.notifications.restoreError'),
      message: error.message
    })
  }
}
```

#### 7. 导入导出功能

**数据导出**:
```typescript
const handleExportData = async () => {
  try {
    const result = await window.electronAPI.dialog.showSaveDialog({
      title: t('settings.export.selectLocation'),
      defaultPath: `paolife-export-${new Date().toISOString().split('T')[0]}.json`,
      filters: [
        { name: 'JSON files', extensions: ['json'] },
        { name: 'All files', extensions: ['*'] }
      ]
    })

    if (!result.canceled && result.filePath) {
      const exportResult = await settingsApi.exportData()
      if (exportResult.success) {
        await window.electronAPI.fs.writeFile(result.filePath, JSON.stringify(exportResult.data, null, 2))
        addNotification({
          type: 'success',
          title: t('settings.notifications.exportSuccess'),
          message: t('settings.notifications.exportSuccessMessage')
        })
      } else {
        throw new Error(exportResult.error)
      }
    }
  } catch (error) {
    addNotification({
      type: 'error',
      title: t('settings.notifications.exportError'),
      message: error.message
    })
  }
}

const handleImportData = async () => {
  try {
    const result = await window.electronAPI.dialog.showOpenDialog({
      title: t('settings.import.selectFile'),
      filters: [
        { name: 'JSON files', extensions: ['json'] },
        { name: 'All files', extensions: ['*'] }
      ],
      properties: ['openFile']
    })

    if (!result.canceled && result.filePaths.length > 0) {
      const confirmed = await window.electronAPI.dialog.showMessageBox({
        type: 'warning',
        title: t('settings.import.confirmTitle'),
        message: t('settings.import.confirmMessage'),
        buttons: [t('common.cancel'), t('common.confirm')],
        defaultId: 0,
        cancelId: 0
      })

      if (confirmed.response === 1) {
        const fileContent = await window.electronAPI.fs.readFile(result.filePaths[0])
        const importData = JSON.parse(fileContent)

        const importResult = await settingsApi.importData(importData)
        if (importResult.success) {
          addNotification({
            type: 'success',
            title: t('settings.notifications.importSuccess'),
            message: t('settings.notifications.importSuccessMessage')
          })
          // 建议重启应用
          setTimeout(() => {
            window.electronAPI.app.restart()
          }, 2000)
        } else {
          throw new Error(importResult.error)
        }
      }
    }
  } catch (error) {
    addNotification({
      type: 'error',
      title: t('settings.notifications.importError'),
      message: error.message
    })
  }
}
```

#### 8. 设置重置和恢复

**重置设置**:
```typescript
const handleResetSettings = async () => {
  const confirmed = await window.electronAPI.dialog.showMessageBox({
    type: 'warning',
    title: t('settings.reset.confirmTitle'),
    message: t('settings.reset.confirmMessage'),
    detail: t('settings.reset.confirmDetail'),
    buttons: [t('common.cancel'), t('settings.reset.confirm')],
    defaultId: 0,
    cancelId: 0
  })

  if (confirmed.response === 1) {
    try {
      resetSettings()
      addNotification({
        type: 'success',
        title: t('settings.notifications.resetSuccess'),
        message: t('settings.notifications.resetSuccessMessage')
      })
      // 重新加载页面状态
      window.location.reload()
    } catch (error) {
      addNotification({
        type: 'error',
        title: t('settings.notifications.resetError'),
        message: error.message
      })
    }
  }
}
```

### 总结

设置页面作为PaoLife应用的配置管理中心，具有以下特点：

1. **全方位配置管理**: 用户信息、编辑器、P.A.R.A.方法论的完整设置
2. **智能化配置**: 自动归档、周回顾、项目模板的智能配置
3. **数据安全管理**: 数据库备份恢复、导入导出的安全机制
4. **用户体验优化**: 实时反馈、确认对话框、自动重启提示
5. **国际化支持**: 多语言界面和主题切换
6. **灵活性配置**: 编辑器模式、专注模式、自动保存的个性化设置

这个设计为用户提供了专业级的应用配置能力，确保应用能够适应不同用户的使用习惯和需求。
